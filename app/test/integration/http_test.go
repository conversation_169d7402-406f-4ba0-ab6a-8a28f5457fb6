package integration_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/information-sharing-networks/signalsd/app/internal/auth"
	"github.com/information-sharing-networks/signalsd/app/internal/database"
	"github.com/jackc/pgx/v5"
)

// TestSignalSubmissionHTTP tests the complete signal submission pipeline via HTTP requests
// to a running signalsd instance. This test covers:
// - HTTP request handling
// - JWT authentication and ISN permission validation
// - JSON schema validation
// - Database persistence (signals, signal_versions, signal_batches)
// - Response formatting
//
// Prerequisites:
// - Docker container running signalsd on localhost:8080
// - Database setup via test-integration.sh
func TestSignalSubmissionHTTP(t *testing.T) {
	// Check if HTTP testing is enabled
	baseURL := os.Getenv("TEST_HTTP_BASE_URL")
	if baseURL == "" {
		t.Skip("HTTP integration test requires TEST_HTTP_BASE_URL environment variable (e.g., http://localhost:8080)")
	}

	// Setup test environment with database and test data
	testEnv := setupHTTPTestData(t)
	defer testEnv.cleanup()

	t.Run("successful signal submission", func(t *testing.T) {
		// Create test signal payload matching the simple integration test schema
		signalPayload := map[string]any{
			"signals": []map[string]any{
				{
					"local_ref": "http-test-signal-001",
					"content": map[string]any{
						"test": "valid content for simple schema",
					},
				},
			},
		}

		// Submit signal via HTTP
		response := testEnv.submitSignals(t, baseURL, signalPayload)
		defer response.Body.Close()

		fmt.Printf("debug response %+v\n", response)

		// Verify response
		if response.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200, got %d", response.StatusCode)
		}

		// Parse response body
		var result map[string]any
		if err := json.NewDecoder(response.Body).Decode(&result); err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}

		// Verify response structure
		if summary, ok := result["summary"].(map[string]any); ok {
			if totalSubmitted, ok := summary["total_submitted"].(float64); ok {
				if totalSubmitted != 1 {
					t.Errorf("Expected 1 signal submitted, got %v", totalSubmitted)
				}
			} else {
				t.Error("Missing total_submitted in summary")
			}
		} else {
			t.Error("Missing summary in response")
		}

		// Verify database persistence
		testEnv.verifySignalInDatabase(t, "http-test-signal-001")
	})

	t.Run("authentication failure", func(t *testing.T) {
		signalPayload := map[string]any{
			"signals": []map[string]any{
				{
					"local_ref": "http-test-signal-002",
					"content":   map[string]any{"test": "content"},
				},
			},
		}

		// Submit without valid token
		response := testEnv.submitSignalsWithToken(t, baseURL, signalPayload, "invalid-token")
		defer response.Body.Close()

		if response.StatusCode != http.StatusUnauthorized {
			t.Errorf("Expected status 401, got %d", response.StatusCode)
		}
	})

	t.Run("schema validation failure", func(t *testing.T) {
		signalPayload := map[string]any{
			"signals": []map[string]any{
				{
					"local_ref": "http-test-signal-003",
					"content": map[string]any{
						"invalid_field": "this should fail schema validation",
						// Missing required "test" field
					},
				},
			},
		}

		response := testEnv.submitSignals(t, baseURL, signalPayload)
		defer response.Body.Close()

		if response.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200 (partial success), got %d", response.StatusCode)
		}

		// Parse response to check for validation errors
		var result map[string]any
		if err := json.NewDecoder(response.Body).Decode(&result); err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}

		// Should have failed signals due to validation
		if results, ok := result["results"].(map[string]any); ok {
			if failedSignals, ok := results["failed_signals"].([]any); ok {
				if len(failedSignals) != 1 {
					t.Errorf("Expected 1 failed signal, got %d", len(failedSignals))
				}
			} else {
				t.Error("Missing failed_signals in results")
			}
		} else {
			t.Error("Missing results in response")
		}
	})

	t.Run("multiple signals batch", func(t *testing.T) {
		signalPayload := map[string]any{
			"signals": []map[string]any{
				{
					"local_ref": "http-test-batch-001",
					"content":   map[string]any{"test": "first signal"},
				},
				{
					"local_ref": "http-test-batch-002",
					"content":   map[string]any{"test": "second signal"},
				},
				{
					"local_ref": "http-test-batch-003",
					"content":   map[string]any{"test": "third signal"},
				},
			},
		}

		response := testEnv.submitSignals(t, baseURL, signalPayload)
		defer response.Body.Close()

		if response.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200, got %d", response.StatusCode)
		}

		// Verify all signals were stored
		testEnv.verifySignalInDatabase(t, "http-test-batch-001")
		testEnv.verifySignalInDatabase(t, "http-test-batch-002")
		testEnv.verifySignalInDatabase(t, "http-test-batch-003")
	})

	t.Run("missing required fields", func(t *testing.T) {
		signalPayload := map[string]any{
			"signals": []map[string]any{
				{
					// Missing local_ref
					"content": map[string]any{"test": "content"},
				},
			},
		}

		response := testEnv.submitSignals(t, baseURL, signalPayload)
		defer response.Body.Close()

		if response.StatusCode != http.StatusBadRequest {
			t.Errorf("Expected status 400, got %d", response.StatusCode)
		}
	})

	t.Run("empty signals array", func(t *testing.T) {
		signalPayload := map[string]any{
			"signals": []map[string]any{},
		}

		response := testEnv.submitSignals(t, baseURL, signalPayload)
		defer response.Body.Close()

		if response.StatusCode != http.StatusBadRequest {
			t.Errorf("Expected status 400, got %d", response.StatusCode)
		}
	})
}

// httpTestData encapsulates the test data setup for HTTP-based integration tests
type httpTestData struct {
	dbConn     *pgx.Conn
	queries    *database.Queries
	authToken  string
	testISN    database.Isn
	signalType database.SignalType
	accountID  uuid.UUID
}

// setupHTTPTestData creates test data using existing integration test helpers
func setupHTTPTestData(t *testing.T) *httpTestData {
	// Setup database connection (reuses existing integration test setup)
	dbConn := setupDbConn(t)
	queries := database.New(dbConn)

	// Create test data using existing helpers
	ownerAccount := createTestAccount(t, queries, "owner", "user", "<EMAIL>")
	testAccount := createTestAccount(t, queries, "member", "service_account", "<EMAIL>")
	testISN := createTestISN(t, queries, "http-test-isn", "HTTP Test ISN", ownerAccount.ID)
	signalType := createTestSignalType(t, queries, testISN.ID, "HTTP Test Signal Type", "1.0.0")

	// Grant write permission to test account
	grantPermission(t, queries, testISN.ID, testAccount.ID, "write")

	// Create auth service and generate token
	// Use the same secret key as the running Docker container
	secretKey := os.Getenv("TEST_SECRET_KEY")
	if secretKey == "" {
		secretKey = "dev-container-secret-key-12345" // Default for Docker development
	}
	authService := auth.NewAuthService(secretKey, "test", queries)
	ctx := auth.ContextWithAccountID(context.Background(), testAccount.ID)
	tokenResponse, err := authService.CreateAccessToken(ctx)
	if err != nil {
		t.Fatalf("Failed to create access token: %v", err)
	}

	return &httpTestData{
		dbConn:     dbConn,
		queries:    queries,
		authToken:  tokenResponse.AccessToken,
		testISN:    testISN,
		signalType: signalType,
		accountID:  testAccount.ID,
	}
}

// cleanup closes database connections
func (env *httpTestData) cleanup() {
	if env.dbConn != nil {
		env.dbConn.Close(context.Background())
	}
}

// submitSignals submits a signal payload using the test account's auth token
func (env *httpTestData) submitSignals(t *testing.T, baseURL string, payload map[string]any) *http.Response {
	fmt.Printf("debug submitSignals %+v env.authToken %s\n", payload, env.authToken)
	return env.submitSignalsWithToken(t, baseURL, payload, env.authToken)
}

// submitSignalsWithToken submits a signal payload with a specific auth token
func (env *httpTestData) submitSignalsWithToken(t *testing.T, baseURL string, payload map[string]any, token string) *http.Response {
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		t.Fatalf("Failed to marshal payload: %v", err)
	}

	url := fmt.Sprintf("%s/api/isn/%s/signal_types/%s/v%s/signals",
		baseURL, env.testISN.Slug, env.signalType.Slug, env.signalType.SemVer)

	fmt.Printf("debug submitSignalsWithToken url %s\n", url)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonPayload))
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}
	fmt.Printf("debug token %s\n", token)

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		t.Fatalf("Failed to submit signals: %v", err)
	}

	return resp
}

// verifySignalInDatabase checks that a signal was properly persisted
func (env *httpTestData) verifySignalInDatabase(t *testing.T, localRef string) {
	ctx := context.Background()

	// Query for the signal using the correct method
	signal, err := env.queries.GetSignalByAccountAndLocalRef(ctx, database.GetSignalByAccountAndLocalRefParams{
		AccountID: env.accountID,
		Slug:      env.signalType.Slug,
		SemVer:    env.signalType.SemVer,
		LocalRef:  localRef,
	})
	if err != nil {
		t.Fatalf("Failed to query signal: %v", err)
	}

	if signal.LocalRef != localRef {
		t.Errorf("Expected local_ref %s, got %s", localRef, signal.LocalRef)
	}

	if signal.IsWithdrawn {
		t.Errorf("Signal should not be withdrawn")
	}
}
