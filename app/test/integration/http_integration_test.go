package integration_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/google/uuid"
	signalsd "github.com/information-sharing-networks/signalsd/app"
	"github.com/information-sharing-networks/signalsd/app/internal/auth"
	"github.com/information-sharing-networks/signalsd/app/internal/database"
	"github.com/information-sharing-networks/signalsd/app/internal/logger"
	"github.com/information-sharing-networks/signalsd/app/internal/server"
	"github.com/information-sharing-networks/signalsd/app/internal/server/isns"
	"github.com/information-sharing-networks/signalsd/app/internal/server/schemas"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog"
)

// TestSignalSubmissionE2E tests the complete signal submission pipeline via HTTP
// This test covers:
// - HTTP request handling
// - JWT authentication and ISN permission validation
// - JSON schema validation
// - Database persistence (signals, signal_versions, signal_batches)
// - Response formatting
func TestSignalSubmissionE2E(t *testing.T) {
	// Setup test environment
	testEnv := setupHTTPTestEnvironment(t)
	defer testEnv.cleanup()

	t.Run("successful signal submission", func(t *testing.T) {
		// Create test signal payload
		signalPayload := map[string]interface{}{
			"signals": []map[string]interface{}{
				{
					"local_ref": "test-signal-001",
					"content": map[string]interface{}{
						"test": "valid content for schema",
					},
				},
			},
		}

		// Submit signal via HTTP
		response := testEnv.submitSignals(t, signalPayload)

		// Verify response
		if response.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200, got %d", response.StatusCode)
		}

		// Parse response body
		var result map[string]interface{}
		if err := json.NewDecoder(response.Body).Decode(&result); err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}

		// Verify response structure
		if summary, ok := result["summary"].(map[string]interface{}); ok {
			if totalSubmitted, ok := summary["total_submitted"].(float64); ok {
				if totalSubmitted != 1 {
					t.Errorf("Expected 1 signal submitted, got %v", totalSubmitted)
				}
			} else {
				t.Error("Missing total_submitted in summary")
			}
		} else {
			t.Error("Missing summary in response")
		}

		// Verify database persistence
		testEnv.verifySignalInDatabase(t, "test-signal-001")
	})

	t.Run("authentication failure", func(t *testing.T) {
		signalPayload := map[string]interface{}{
			"signals": []map[string]interface{}{
				{
					"local_ref": "test-signal-002",
					"content":   map[string]interface{}{"test": "content"},
				},
			},
		}

		// Submit without valid token
		response := testEnv.submitSignalsWithToken(t, signalPayload, "invalid-token")

		if response.StatusCode != http.StatusUnauthorized {
			t.Errorf("Expected status 401, got %d", response.StatusCode)
		}
	})

	t.Run("schema validation failure", func(t *testing.T) {
		signalPayload := map[string]interface{}{
			"signals": []map[string]interface{}{
				{
					"local_ref": "test-signal-003",
					"content": map[string]interface{}{
						"invalid_field": "this should fail schema validation",
					},
				},
			},
		}

		response := testEnv.submitSignals(t, signalPayload)

		if response.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200 (partial success), got %d", response.StatusCode)
		}

		// Parse response to check for validation errors
		var result map[string]interface{}
		if err := json.NewDecoder(response.Body).Decode(&result); err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}

		// Should have failed signals due to validation
		if results, ok := result["results"].(map[string]interface{}); ok {
			if failedSignals, ok := results["failed_signals"].([]interface{}); ok {
				if len(failedSignals) != 1 {
					t.Errorf("Expected 1 failed signal, got %d", len(failedSignals))
				}
			} else {
				t.Error("Missing failed_signals in results")
			}
		} else {
			t.Error("Missing results in response")
		}
	})
}

// httpTestEnvironment encapsulates the test setup for HTTP-based integration tests
type httpTestEnvironment struct {
	server     *httptest.Server
	dbPool     *pgxpool.Pool
	queries    *database.Queries
	authToken  string
	testISN    database.Isn
	signalType database.SignalType
	accountID  uuid.UUID
}

// setupHTTPTestEnvironment creates a complete test environment with HTTP server and database
func setupHTTPTestEnvironment(t *testing.T) *httpTestEnvironment {
	// Get database URL from environment (set by test-integration.sh)
	dbURL := os.Getenv("TEST_DATABASE_URL")
	if dbURL == "" {
		t.Skip("Integration test requires TEST_DATABASE_URL environment variable")
	}

	ctx := context.Background()

	// Create database pool
	dbPool, err := pgxpool.New(ctx, dbURL)
	if err != nil {
		t.Fatalf("Failed to create database pool: %v", err)
	}

	queries := database.New(dbPool)

	// Create test data using existing helpers
	ownerAccount := createTestAccount(t, queries, "owner", "user", "<EMAIL>")
	testAccount := createTestAccount(t, queries, "member", "service_account", "<EMAIL>")
	testISN := createTestISN(t, queries, "test-isn", "Test ISN", ownerAccount.ID)
	signalType := createTestSignalType(t, queries, testISN.ID, "Test Signal Type", "1.0.0")

	// Grant write permission to test account
	grantPermission(t, queries, testISN.ID, testAccount.ID, "write")

	// Create auth service and generate token
	authService := auth.NewAuthService("test-secret-key", "test", queries)
	ctx = auth.ContextWithAccountID(ctx, testAccount.ID)
	tokenResponse, err := authService.CreateAccessToken(ctx)
	if err != nil {
		t.Fatalf("Failed to create access token: %v", err)
	}

	// Create server configuration - using the actual ServerConfig struct from signalsd package
	cfg := &signalsd.ServerConfig{
		ServiceMode:          "signals",
		MaxSignalPayloadSize: 5 * 1024 * 1024, // 5MB
		MaxAPIRequestSize:    64 * 1024,       // 64KB
		ReadTimeout:          10 * time.Second,
		WriteTimeout:         10 * time.Second,
		IdleTimeout:          60 * time.Second,
	}

	// Create CORS configs
	corsConfigs := &signalsd.CORSConfigs{
		// For testing, we'll use nil CORS configs - the server will handle this gracefully
		Protected: nil,
		Public:    nil,
	}

	// Create loggers
	serverLogger := logger.InitServerLogger()
	httpLogger := logger.InitHttpLogger(zerolog.DebugLevel, "test")

	// Create schema cache
	schemaCache := schemas.NewSchemaCache()

	// Create public ISN cache
	publicIsnCache := isns.NewPublicIsnCache()

	// Create and start HTTP test server
	srv := server.NewServer(
		dbPool,
		queries,
		authService,
		cfg,
		corsConfigs,
		serverLogger,
		httpLogger,
		schemaCache,
		publicIsnCache,
	)

	// For testing, we need to access the router. Let's create a simple handler
	// that wraps the server's functionality
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// This is a simplified approach - in a real implementation,
		// we'd want the server to expose its router
		w.WriteHeader(http.StatusNotImplemented)
		w.Write([]byte("Test server not fully implemented"))
	})

	httpServer := httptest.NewServer(testHandler)

	env := &httpTestEnvironment{
		server:     httpServer,
		dbPool:     dbPool,
		queries:    queries,
		authToken:  tokenResponse.AccessToken,
		testISN:    testISN,
		signalType: signalType,
		accountID:  testAccount.ID,
	}

	return env
}

// cleanup closes database connections and shuts down the test server
func (env *httpTestEnvironment) cleanup() {
	if env.server != nil {
		env.server.Close()
	}
	if env.dbPool != nil {
		env.dbPool.Close()
	}
}

// submitSignals submits a signal payload using the test account's auth token
func (env *httpTestEnvironment) submitSignals(t *testing.T, payload map[string]interface{}) *http.Response {
	return env.submitSignalsWithToken(t, payload, env.authToken)
}

// submitSignalsWithToken submits a signal payload with a specific auth token
func (env *httpTestEnvironment) submitSignalsWithToken(t *testing.T, payload map[string]interface{}, token string) *http.Response {
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		t.Fatalf("Failed to marshal payload: %v", err)
	}

	url := fmt.Sprintf("%s/api/isn/%s/signal_types/%s/v%s/signals",
		env.server.URL, env.testISN.Slug, env.signalType.Slug, env.signalType.SemVer)

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonPayload))
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		t.Fatalf("Failed to submit signals: %v", err)
	}

	return resp
}

// verifySignalInDatabase checks that a signal was properly persisted
func (env *httpTestEnvironment) verifySignalInDatabase(t *testing.T, localRef string) {
	ctx := context.Background()

	// Query for the signal using the correct method
	signal, err := env.queries.GetSignalByAccountAndLocalRef(ctx, database.GetSignalByAccountAndLocalRefParams{
		AccountID: env.accountID,
		Slug:      env.signalType.Slug,
		SemVer:    env.signalType.SemVer,
		LocalRef:  localRef,
	})
	if err != nil {
		t.Fatalf("Failed to query signal: %v", err)
	}

	if signal.LocalRef != localRef {
		t.Errorf("Expected local_ref %s, got %s", localRef, signal.LocalRef)
	}

	if signal.IsWithdrawn {
		t.Errorf("Signal should not be withdrawn")
	}
}
