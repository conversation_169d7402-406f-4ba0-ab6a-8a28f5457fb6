#!/bin/bash
# HTTP integration test runner for signalsd
# Tests HTTP endpoints against a running signalsd Docker container

usage() {
    echo "usage: $0 [ -h -s -c ] [ -n regexp ] [ -u base_url ]
    -h: show usage
    -s: skip database setup (this will delete the database content rather than dropping and recreating it)
    -c: clean up the test database after running the tests
    -n: run only tests matching the regexp
    -u: base URL for HTTP tests (default: http://localhost:8080)
    "
    exit $1
}

cleanup() {
    docker compose -f ../../docker-compose.yml exec -it db psql -U $POSTGRES_USER -d postgres -c "DROP DATABASE IF EXISTS $DB_NAME;"
}

check_docker_is_running() {
    if ! docker compose -f ../../docker-compose.yml ps app db >/dev/null 2>&1; then
        return 1
    fi
    return 0
}

check_signalsd_is_running() {
    if ! curl -s -f "${BASE_URL}/health/live" >/dev/null 2>&1; then
        return 1
    fi
    return 0
}

drop_and_recreate_db() {
    docker compose -f ../../docker-compose.yml exec -it db psql -U $POSTGRES_USER -d postgres -c "DROP DATABASE IF EXISTS $DB_NAME;"  && \
    docker compose -f ../../docker-compose.yml exec -it db psql -U $POSTGRES_USER -d postgres -c "CREATE DATABASE $DB_NAME;"
}

delete_data() {
    docker compose -f ../../docker-compose.yml exec -it db psql -U $POSTGRES_USER -d $DB_NAME -c "DELETE FROM accounts CASCADE;"
}

run_migration() {
    docker compose -f ../../docker-compose.yml exec app bash -c "cd /signalsd/app && goose -dir sql/schema postgres postgres://${POSTGRES_USER}:@db:5432/${DB_NAME}?sslmode=disable up"
}

# Parse command line arguments
SKIP_DB_SETUP=""
CLEANUP=""
FUNCTION_NAME=".*"
BASE_URL="http://localhost:8080"

while getopts "hscn:u:" opt; do
    case $opt in
        h)
            usage 0
            ;;
        s)
            SKIP_DB_SETUP=1
            ;;
        c)
            CLEANUP=1
            ;;
        n)
            FUNCTION_NAME="$OPTARG"
            ;;
        u)
            BASE_URL="$OPTARG"
            ;;
        \?)
            echo "Invalid option: -$OPTARG" >&2
            usage 1
            ;;
    esac
done

# Configuration
POSTGRES_USER="signalsd-dev"
POSTGRES_PASSWORD=""
HOST="localhost"
PORT="15432"
DB_NAME="signalsd_integration_test"

echo "🚀 Running signalsd HTTP integration tests"
echo "📍 Target URL: $BASE_URL"

# Check if Docker containers are running
if ! check_docker_is_running; then
    echo "❌ Docker containers are not running. Please start them with:"
    echo "   docker compose -f ../../docker-compose.yml up -d"
    exit 1
fi

# Check if signalsd is responding
if ! check_signalsd_is_running "$BASE_URL"; then
    echo "❌ signalsd is not responding at $BASE_URL"
    echo "   Make sure the signalsd container is running and accessible"
    exit 1
fi

export TEST_DATABASE_URL="postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${HOST}:${PORT}/${DB_NAME}?sslmode=disable"
export TEST_HTTP_BASE_URL="$BASE_URL"

echo "🗄️  Database: $TEST_DATABASE_URL"

if [ "$SKIP_DB_SETUP" ]; then
    echo "⚙️ deleting data on $DB_NAME database"
    delete_data
    if [ $? -ne 0 ]; then
        echo "❌ Failed to delete data"
        exit 1
    fi
else
    echo "⚙️ dropping and recreating database: $DB_NAME"
    drop_and_recreate_db
    if [ $? -ne 0 ]; then
        echo "❌ Failed to (re)create database $DB_NAME"
        exit 1
    fi
    echo "⚙️ running migrations on $DB_NAME database"
    run_migration
    if [ $? -ne 0 ]; then
        echo "❌ Failed to apply migrations"
        exit 1
    fi
fi

echo
echo "⚙️️ Running HTTP integration tests..."
echo
# Run the HTTP integration tests from the app directory where go.mod is located
cd ../../app && go test -v ./test/integration -run "$FUNCTION_NAME" -timeout 60s
test_result=$?

if [ "$CLEANUP" ];then
    echo "⚙️ Cleaning up test database..."
    cleanup
fi

if [ $test_result -ne 0 ]; then
    echo "❌ HTTP integration tests failed"
    exit 1
else
    echo "✅ HTTP integration tests completed successfully!"
fi
