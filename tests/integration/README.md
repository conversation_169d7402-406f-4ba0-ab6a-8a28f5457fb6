# Integration Tests

## Overview

Integration tests for the authentication system.

### Prerequisites
The script uses the Docker development setup (see the main README for the project). The app and db containers are already configured with the dependencies needed to run these tests.

to start the app, use:
```sh
docker compose up 
```

### Usage

```bash
cd tests/integration

# Run all integration tests
./test-integration.sh

# Run a specific test and clean up the database on exit
./test-integration.sh -n TestAccessTokenIntegration -c

# Skip database setup (faster for repeated runs)
./test-integration.sh -s

# Show usage statement
./test-integration.sh -h
```

**Docker Environment**
The script uses the Docker development setup (see the main README for the project).  The app and db containers are already configured with the dependencies needed to run these tests.

## Test Structure

Integration tests are in `app/integration_test.go` and follow the pattern:

1. Connect to pre-created test database
2. Set up test data (accounts, ISNs, permissions)
3. Test complete authentication flows
4. Verify database state and JWT tokens
5. Clean up connections (database cleanup handled by script)