# Integration Tests

## Overview

Integration tests for the authentication system.

### Prerequisites
The script uses the Docker development setup (see the main README for the project). The app and db containers are already configured with the dependencies needed to run these tests.

to start the app, use:
```sh
docker compose up 
```

### Usage

```bash
cd tests/integration

# Run all integration tests
./test-integration.sh

./test-integration.sh -c

# Run a specific test and clean up the database on exit
./test-integration.sh -n TestAccessTokenIntegration -c

# Skip database setup (faster for repeated runs)
./test-integration.sh -s

# Show usage statement
./test-integration.sh -h
```

**Docker Environment**
The script uses the Docker development setup (see the main README for the project).  The app and db containers are already configured with the dependencies needed to run these tests.

## Test Structure

Integration tests are in `app/integration_test.go` and follow the pattern:

1. Connect to pre-created test database
2. Set up test data (accounts, ISNs, permissions)
3. Test complete authentication flows
4. Verify database state and JWT tokens
5. Clean up connections (database cleanup handled by script)

This approach tests the real complexity - database queries, relationships, and data handling - where most bugs actually occur.

## HTTP Integration Tests

HTTP integration tests make real HTTP requests to a running signalsd Docker container, testing the complete end-to-end signal submission pipeline.

### Running HTTP Integration Tests

**Prerequisites:**
- Docker containers running: `docker compose up -d`
- signalsd accessible on localhost:8080

**Basic Usage:**

```bash
# From the tests/integration directory
cd tests/integration

# Run all HTTP integration tests
./test-http-integration.sh

# Run specific test
./test-http-integration.sh -n TestSignalSubmissionHTTP

# Use custom base URL
./test-http-integration.sh -u http://localhost:9090

# Skip database setup (faster for repeated runs)
./test-http-integration.sh -s

# Clean up database after tests
./test-http-integration.sh -c
```

### What HTTP Tests Cover

- **Complete signal submission pipeline** via real HTTP requests
- **Authentication and authorization** with JWT tokens
- **JSON schema validation** against GitHub-hosted schemas
- **Database persistence** verification
- **Error handling** for various failure scenarios
- **Batch processing** with multiple signals

### Test Scenarios

1. **Successful signal submission** - Valid payload, proper authentication
2. **Authentication failure** - Invalid/missing JWT tokens
3. **Schema validation failure** - Invalid signal content
4. **Multiple signals batch** - Batch processing verification
5. **Missing required fields** - Malformed request handling
6. **Empty signals array** - Edge case validation

### Environment Variables

The HTTP test script sets these environment variables:

- `TEST_DATABASE_URL` - Database connection for verification
- `TEST_HTTP_BASE_URL` - Target signalsd instance URL

### Integration with Docker

HTTP tests work with your existing Docker setup:
- Uses the same database as regular integration tests
- Makes requests to the running signalsd container
- Verifies results by querying the database directly

This approach tests the actual deployed configuration rather than test approximations.