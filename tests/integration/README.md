# Integration Tests

## Overview

Integration tests for the authentication system that test the complete flow with a real PostgreSQL database.

## Running Integration Tests

### Prerequisites

- <PERSON><PERSON> and Docker Compose running
- PostgreSQL container available via docker-compose.yml

### Basic Usage

```bash
# From the tests/integration directory
cd tests/integration

# Run all integration tests
./test-integration.sh

# Run specific test
./test-integration.sh -n TestAccessTokenIntegration

# Skip database setup (faster for repeated runs)
./test-integration.sh -s

# Clean up database after tests
./test-integration.sh -c

# Show help
./test-integration.sh -h
```

### Environment Presets

The script includes presets that match the main README documentation:

**Local Environment (`-e local`)**
- Connection: `postgres://$(whoami):@localhost:5432/signalsd_admin`
- Matches the local development setup from the main README
- Uses your macOS username for authentication

**Docker Environment (`-e docker`)**
- Connection: `postgres://signalsd-dev@localhost:15432/signalsd_admin`
- Matches the Docker development setup from the main README
- Uses the Docker container's database on port 15432

### What the Script Does

1. **Database Setup**: Creates `signalsd_integration_test` database (drops if exists)
2. **Schema Application**: Uses `goose` to apply migrations from `sql/schema/`
3. **Test Execution**: Runs Go tests with `-run "Integration"`
4. **Cleanup**: Drops the test database

### Environment Variables Set by Script

The script sets this environment variable for the tests:

- `TEST_DATABASE_URL` - Full connection string to the test database

### Migration System

The script uses `goose` to apply database migrations with environment-specific commands:

**Local Environment:**
```bash
goose -dir ../../app/sql/schema postgres "$TEST_DB_URL" up
```

**Docker Environment:**
```bash
docker compose exec app bash -c "cd /signalsd/app && goose -dir sql/schema postgres \"$TEST_DB_URL\" up"
```

**Prerequisites:**
- **Local**: `goose` installed locally: `go install github.com/pressly/goose/v3/cmd/goose@latest`
- **Docker**: Docker app container running: `docker compose up app -d`

## Test Structure

Integration tests are in `app/integration_test.go` and follow the pattern:

1. Connect to pre-created test database
2. Set up test data (accounts, ISNs, permissions)
3. Test complete authentication flows
4. Verify database state and JWT tokens
5. Clean up connections (database cleanup handled by script)

This approach tests the real complexity - database queries, relationships, and data handling - where most bugs actually occur.
